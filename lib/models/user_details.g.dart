// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_details.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserDetails _$UserDetailsFromJson(Map<String, dynamic> json) => UserDetails(
      avatarColor: json['avatar_color'] as String,
      phone: (json['phone'] as num).toInt(),
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      village: json['village'] == null
          ? null
          : Circle.fromJson(json['village'] as Map<String, dynamic>),
      shortBio: json['short_bio'] as String? ?? '',
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      gender: (json['gender_new'] as num?)?.toInt(),
      badge: json['badge'] == null
          ? null
          : Badge.fromJson(json['badge'] as Map<String, dynamic>),
      loggedInUser: json['loggedInUser'] as bool,
      verified: json['verified'] as bool,
      follows: json['follows'] as bool,
      postsCount: (json['posts_count'] as num).toInt(),
      mentionsCount: (json['mentions_count'] as num).toInt(),
      likesCount: (json['likes_count'] as num).toInt(),
      followersCount: (json['followers_count'] as num).toInt(),
      followingCount: (json['following_count'] as num).toInt(),
      photo: json['photo'] == null
          ? null
          : Photo.fromJson(json['photo'] as Map<String, dynamic>),
      blocked: json['blocked'] as bool? ?? false,
      circles: (json['circles'] as List<dynamic>?)
              ?.map((e) => Circle.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      loggedInUserBlocked: json['logged_in_user_blocked'] as bool? ?? false,
      showMessageAction: json['show_message_action'] as bool? ?? false,
      notifyBadgeIssue: json['notify_badge_issue'] as bool? ?? false,
      internalJournalist: json['internal_journalist'] as bool? ?? false,
      referralPoints: (json['referral_points'] as num?)?.toInt() ?? 0,
      isPremium: json['is_premium'] as bool? ?? false,
      showPremiumGoldIcon: json['show_premium_gold_icon'] as bool? ?? false,
      eligibleForPremium: json['eligible_for_premium'] as bool? ?? false,
    );

Map<String, dynamic> _$UserDetailsToJson(UserDetails instance) {
  final val = <String, dynamic>{
    'avatar_color': instance.avatarColor,
    'phone': instance.phone,
    'id': instance.id,
    'name': instance.name,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('village', instance.village?.toJson());
  val['short_bio'] = instance.shortBio;
  writeNotNull('created_at', instance.createdAt?.toIso8601String());
  writeNotNull('gender_new', instance.gender);
  writeNotNull('badge', instance.badge?.toJson());
  val['loggedInUser'] = instance.loggedInUser;
  val['verified'] = instance.verified;
  val['follows'] = instance.follows;
  val['posts_count'] = instance.postsCount;
  val['mentions_count'] = instance.mentionsCount;
  val['likes_count'] = instance.likesCount;
  val['followers_count'] = instance.followersCount;
  val['following_count'] = instance.followingCount;
  writeNotNull('photo', instance.photo?.toJson());
  val['blocked'] = instance.blocked;
  val['circles'] = instance.circles.map((e) => e.toJson()).toList();
  val['logged_in_user_blocked'] = instance.loggedInUserBlocked;
  val['show_message_action'] = instance.showMessageAction;
  val['notify_badge_issue'] = instance.notifyBadgeIssue;
  val['internal_journalist'] = instance.internalJournalist;
  val['referral_points'] = instance.referralPoints;
  val['is_premium'] = instance.isPremium;
  val['show_premium_gold_icon'] = instance.showPremiumGoldIcon;
  val['eligible_for_premium'] = instance.eligibleForPremium;
  return val;
}
