// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'circle_about.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CircleAbout _$CircleAboutFromJson(Map<String, dynamic> json) => CircleAbout(
      aboutMyLeader: json['about_leader'] == null
          ? null
          : AboutMyLeader.fromJson(
              json['about_leader'] as Map<String, dynamic>),
      leadershipPositions: json['positions_section'] == null
          ? null
          : LeaderPositions.fromJson(
              json['positions_section'] as Map<String, dynamic>),
      projectsSection: json['projects_section'] == null
          ? null
          : ProjectSection.fromJson(
              json['projects_section'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CircleAboutToJson(CircleAbout instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('about_leader', instance.aboutMyLeader?.toJson());
  writeNotNull('positions_section', instance.leadershipPositions?.toJson());
  writeNotNull('projects_section', instance.projectsSection?.toJson());
  return val;
}
