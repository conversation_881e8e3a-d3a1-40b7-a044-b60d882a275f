// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Post _$PostFromJson(Map<String, dynamic> json) => Post(
      id: (json['id'] as num).toInt(),
      content: json['content'] as String?,
      circle: json['circle'] == null
          ? null
          : Circle.fromJson(json['circle'] as Map<String, dynamic>),
      hashId: json['hashid'] as String,
      feedReason: json['feed_reason'] as String? ?? '',
      likesCount: (json['likes_count'] as num?)?.toInt() ?? 0,
      whatsappCount: (json['whatsapp_count'] as num?)?.toInt() ?? 0,
      commentsCount: (json['comments_count'] as num?)?.toInt() ?? 0,
      opinionsCount: (json['opinions_count'] as num?)?.toInt() ?? 0,
      viewsCount: (json['views_count'] as num?)?.toInt() ?? 0,
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      userLiked: json['user_liked'] as bool? ?? false,
      userCircled: json['user_circled'] as bool? ?? false,
      userSeen: json['user_seen'] as bool? ?? false,
      isLoggedInUserPost: json['is_logged_in_user_post'] as bool? ?? false,
      isLoggedInUserForward:
          json['is_logged_in_user_forward'] as bool? ?? false,
      isPoll: json['is_poll'] as bool? ?? false,
      active: json['active'] as bool? ?? false,
      commentsEnabled: json['enable_comments'] as bool? ?? false,
      commentsConfig: json['comments_config'] == null
          ? null
          : CommentsConfig.fromJson(
              json['comments_config'] as Map<String, dynamic>),
      selectedCommentOption: json['comments_type'] as String? ?? '',
      createdAt: DateTime.parse(json['created_at'] as String),
      customProperties:
          json['custom_properties'] as Map<String, dynamic>? ?? {},
      followingLikedUsers: (json['liked_users'] as List<dynamic>?)
              ?.map((e) => User.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      photos: (json['photos'] as List<dynamic>?)
              ?.map((e) => Photo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      videos: (json['videos'] as List<dynamic>?)
              ?.map((e) => Video.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      comments: (json['comments'] as List<dynamic>?)
              ?.map((e) => Comment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      previewComments: (json['preview_comments'] as List<dynamic>?)
              ?.map((e) => Comment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      hashtags: (json['hashtags'] as List<dynamic>?)
              ?.map((e) => PostHashtag.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      link: json['link'] == null
          ? null
          : LinkPreview.fromJson(json['link'] as Map<String, dynamic>),
      customGradientValues: CustomGradient.fromJson(
          json['post_share_card_gradients'] as Map<String, dynamic>),
      shareCardText: ShareCardText.fromJson(
          json['post_share_card_text'] as Map<String, dynamic>),
      readMoreText:
          json['read_more_text'] as String? ?? '...Praja App ‌లో మరింత చదవండి',
      profileUploadToolTip: json['photo_upload_tool_tip'] as String? ??
          'మీ ప్రొఫైల్ ఫోటోను జోడించండి',
      showPostShareCard: json['show_post_card_flag'] as bool? ?? false,
      parentPost: json['parent_post'] == null
          ? null
          : Post.fromJson(json['parent_post'] as Map<String, dynamic>),
      taggedCircles: (json['tagged_circles'] as List<dynamic>?)
              ?.map((e) => Circle.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      adminOptions: json['admin_options'] == null
          ? null
          : AdminOptionsConfig.fromJson(
              json['admin_options'] as Map<String, dynamic>),
      taggedCircleIds: json['tagged_circles_ids'] as List<dynamic>? ?? [],
      isPostRemoved: json['is_post_removed'] as bool? ?? false,
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$PostToJson(Post instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['id'] = instance.id;
  writeNotNull('content', instance.content);
  writeNotNull('circle', instance.circle?.toJson());
  val['hashid'] = instance.hashId;
  val['feed_reason'] = instance.feedReason;
  val['likes_count'] = instance.likesCount;
  val['whatsapp_count'] = instance.whatsappCount;
  val['comments_count'] = instance.commentsCount;
  val['opinions_count'] = instance.opinionsCount;
  val['views_count'] = instance.viewsCount;
  val['user'] = instance.user.toJson();
  val['user_liked'] = instance.userLiked;
  val['user_circled'] = instance.userCircled;
  val['user_seen'] = instance.userSeen;
  val['is_logged_in_user_post'] = instance.isLoggedInUserPost;
  val['is_logged_in_user_forward'] = instance.isLoggedInUserForward;
  val['is_poll'] = instance.isPoll;
  val['active'] = instance.active;
  val['enable_comments'] = instance.commentsEnabled;
  writeNotNull('comments_config', instance.commentsConfig?.toJson());
  val['comments_type'] = instance.selectedCommentOption;
  val['created_at'] = instance.createdAt.toIso8601String();
  val['custom_properties'] = instance.customProperties;
  val['liked_users'] =
      instance.followingLikedUsers.map((e) => e.toJson()).toList();
  val['photos'] = instance.photos.map((e) => e.toJson()).toList();
  val['videos'] = instance.videos.map((e) => e.toJson()).toList();
  val['comments'] = instance.comments.map((e) => e.toJson()).toList();
  val['preview_comments'] =
      instance.previewComments.map((e) => e.toJson()).toList();
  val['hashtags'] = instance.hashtags.map((e) => e.toJson()).toList();
  writeNotNull('link', instance.link?.toJson());
  val['post_share_card_gradients'] = instance.customGradientValues.toJson();
  val['post_share_card_text'] = instance.shareCardText.toJson();
  val['read_more_text'] = instance.readMoreText;
  val['photo_upload_tool_tip'] = instance.profileUploadToolTip;
  val['show_post_card_flag'] = instance.showPostShareCard;
  writeNotNull('parent_post', instance.parentPost?.toJson());
  val['tagged_circles'] =
      instance.taggedCircles.map((e) => e.toJson()).toList();
  writeNotNull('admin_options', instance.adminOptions?.toJson());
  val['tagged_circles_ids'] = instance.taggedCircleIds;
  val['is_post_removed'] = instance.isPostRemoved;
  return val;
}
