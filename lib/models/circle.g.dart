// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'circle.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Circle _$CircleFromJson(Map<String, dynamic> json) => Circle(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      nameEN: json['name_en'] as String? ?? '',
      level: json['level'] as String? ?? 'unknown',
      typeOfOrganisation: json['type_of_organisation'] as String?,
      hashId: json['hashid'] as String?,
      circleInfoUrl: json['circle_info_url'] as String?,
      membersCount: (json['members_count'] as num?)?.toInt() ?? 0,
      newPostsCount: (json['new_posts_count'] as num?)?.toInt() ?? 0,
      type: json['circle_type'] as String? ?? 'unknown',
      circleBanner: json['circle_banner'] as String?,
      shortName: json['short_name'] as String?,
      intro: json['intro'] as String?,
      description: json['description'] as String?,
      headUser: json['head_user'] == null
          ? null
          : User.fromJson(json['head_user'] as Map<String, dynamic>),
      profilePhoto: json['profile_photo'] == null
          ? null
          : Photo.fromJson(json['profile_photo'] as Map<String, dynamic>),
      isFanGroup: json['is_fan_group'] as bool? ?? false,
      poster: json['poster'] == null
          ? null
          : Poster.fromJson(json['poster'] as Map<String, dynamic>),
      badgeCard: json['badge_card'] == null
          ? null
          : BadgeCard.fromJson(json['badge_card'] as Map<String, dynamic>),
      preloadedFeedItems: (json['preload_feed_items'] as List<dynamic>?)
              ?.map((e) => FeedItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      tagCircleConfig: json['circle_tag_config'] == null
          ? null
          : TagCircleConfig.fromJson(
              json['circle_tag_config'] as Map<String, dynamic>),
      lastPostedAt: json['last_posted_at'] == null
          ? null
          : DateTime.parse(json['last_posted_at'] as String),
      isUserJoined: json['is_user_joined'] as bool? ?? false,
      locationInfo: json['sub_header'] as String?,
      posts: (json['posts'] as List<dynamic>?)
              ?.map((e) => Post.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      tabs: (json['circle_tabs'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$CircleTabsEnumMap, e))
              .toList() ??
          [],
      parents: (json['parents'] as List<dynamic>?)
              ?.map((e) => Circle.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      leaderPhotos: (json['leader_photos'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      circleBackground: json['circle_background'] == null
          ? null
          : CircleBackground.fromJson(
              json['circle_background'] as Map<String, dynamic>),
      feedOptions: (json['feed_options'] as List<dynamic>?)
              ?.map((e) => FeedOption.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      showSuggestedUsersList:
          json['show_suggested_users_list'] as bool? ?? false,
      tagIndex: json['tag_index'] as String?,
      allowAddToHomescreen: json['allow_add_to_homescreen'] as bool? ?? false,
      backingFieldConversationType: json['conversation_type'] as String?,
    );

Map<String, dynamic> _$CircleToJson(Circle instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'name': instance.name,
    'name_en': instance.nameEN,
    'level': instance.level,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('type_of_organisation', instance.typeOfOrganisation);
  writeNotNull('hashid', instance.hashId);
  writeNotNull('circle_info_url', instance.circleInfoUrl);
  val['members_count'] = instance.membersCount;
  writeNotNull('new_posts_count', instance.newPostsCount);
  val['circle_type'] = instance.type;
  writeNotNull('circle_banner', instance.circleBanner);
  writeNotNull('short_name', instance.shortName);
  writeNotNull('intro', instance.intro);
  writeNotNull('description', instance.description);
  writeNotNull('head_user', instance.headUser?.toJson());
  writeNotNull('profile_photo', instance.profilePhoto?.toJson());
  val['is_fan_group'] = instance.isFanGroup;
  writeNotNull('poster', instance.poster?.toJson());
  writeNotNull('badge_card', instance.badgeCard?.toJson());
  val['preload_feed_items'] =
      instance.preloadedFeedItems.map((e) => e.toJson()).toList();
  writeNotNull('circle_tag_config', instance.tagCircleConfig?.toJson());
  writeNotNull('last_posted_at', instance.lastPostedAt?.toIso8601String());
  val['is_user_joined'] = instance.isUserJoined;
  writeNotNull('sub_header', instance.locationInfo);
  writeNotNull('posts', instance.posts?.map((e) => e.toJson()).toList());
  val['circle_tabs'] =
      instance.tabs.map((e) => _$CircleTabsEnumMap[e]!).toList();
  val['parents'] = instance.parents.map((e) => e.toJson()).toList();
  val['leader_photos'] = instance.leaderPhotos;
  writeNotNull('circle_background', instance.circleBackground?.toJson());
  val['feed_options'] = instance.feedOptions.map((e) => e.toJson()).toList();
  val['show_suggested_users_list'] = instance.showSuggestedUsersList;
  writeNotNull('tag_index', instance.tagIndex);
  writeNotNull('conversation_type', instance.backingFieldConversationType);
  val['allow_add_to_homescreen'] = instance.allowAddToHomescreen;
  return val;
}

const _$CircleTabsEnumMap = {
  CircleTabs.posts: 'posts',
  CircleTabs.info: 'info',
  CircleTabs.members: 'members',
  CircleTabs.unknown: 'NONE',
};
