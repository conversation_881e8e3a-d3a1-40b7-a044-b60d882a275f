// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hashtag.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Hashtag _$HashtagFromJson(Map<String, dynamic> json) => Hashtag(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      hashId: json['hashid'] as String? ?? '',
      circles: (json['new_circles'] as List<dynamic>?)
          ?.map((e) => Circle.fromJson(e as Map<String, dynamic>))
          .toList(),
      userLiked: json['user_liked'] as bool? ?? false,
      userCircled: json['user_circled'] as bool? ?? false,
      likesCount: (json['likes_count'] as num?)?.toInt() ?? 0,
      opinionsCount: (json['opinions_count'] as num?)?.toInt() ?? 0,
      whatsappCount: (json['whatsapp_count'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$HashtagToJson(Hashtag instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'name': instance.name,
    'hashid': instance.hashId,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull(
      'new_circles', instance.circles?.map((e) => e.toJson()).toList());
  val['user_liked'] = instance.userLiked;
  val['user_circled'] = instance.userCircled;
  val['likes_count'] = instance.likesCount;
  val['opinions_count'] = instance.opinionsCount;
  val['whatsapp_count'] = instance.whatsappCount;
  writeNotNull('created_at', instance.createdAt?.toIso8601String());
  return val;
}
