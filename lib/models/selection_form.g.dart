// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'selection_form.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SelectionForm _$SelectionFormFromJson(Map<String, dynamic> json) =>
    SelectionForm(
      feedItemId: json['feed_item_id'] as String?,
      feedType: json['feed_type'] as String,
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['image_url'] as String?,
      imageWidth: (json['image_width'] as num?)?.toInt() ?? 100,
      imageHeight: (json['image_height'] as num?)?.toInt() ?? 100,
      ctaText: json['cta_text'] as String,
      options: (json['options'] as List<dynamic>)
          .map((e) => Option.fromJson(e as Map<String, dynamic>))
          .toList(),
      allowMultipleSelection:
          json['allow_multiple_selection'] as bool? ?? false,
      submitUrl: json['submit_url'] as String,
      postSubmitAction: $enumDecodeNullable(
              _$SelectionFormPostSubmitActionEnumMap,
              json['post_submit_action']) ??
          SelectionFormPostSubmitAction.dismiss,
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
    );

Map<String, dynamic> _$SelectionFormToJson(SelectionForm instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['title'] = instance.title;
  val['subtitle'] = instance.subtitle;
  writeNotNull('image_url', instance.imageUrl);
  val['image_width'] = instance.imageWidth;
  val['image_height'] = instance.imageHeight;
  val['cta_text'] = instance.ctaText;
  val['options'] = instance.options.map((e) => e.toJson()).toList();
  val['allow_multiple_selection'] = instance.allowMultipleSelection;
  val['submit_url'] = instance.submitUrl;
  val['post_submit_action'] =
      _$SelectionFormPostSubmitActionEnumMap[instance.postSubmitAction]!;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}

const _$SelectionFormPostSubmitActionEnumMap = {
  SelectionFormPostSubmitAction.dismiss: 'dismiss',
  SelectionFormPostSubmitAction.refresh: 'refresh',
};

Option _$OptionFromJson(Map<String, dynamic> json) => Option(
      id: json['id'] as String,
      value: json['value'] as String,
    );

Map<String, dynamic> _$OptionToJson(Option instance) => <String, dynamic>{
      'id': instance.id,
      'value': instance.value,
    };
