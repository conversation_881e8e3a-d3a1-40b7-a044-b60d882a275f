// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fan_requests_screen_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FanRequestsScreenResponse _$FanRequestsScreenResponseFromJson(
        Map<String, dynamic> json) =>
    FanRequestsScreenResponse(
      creative:
          PosterCreative.fromJson(json['creative'] as Map<String, dynamic>),
      layouts: (json['layouts'] as List<dynamic>)
          .map((e) => PosterLayout.fromJson(e as Map<String, dynamic>))
          .toList(),
      requestedUsers: FanPostersRequestedUsers.fromJson(
          json['requested_users'] as Map<String, dynamic>),
      leadSectionDetails: LeadSectionDetails.fromJson(
          json['lead_section_details'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FanRequestsScreenResponseToJson(
        FanRequestsScreenResponse instance) =>
    <String, dynamic>{
      'creative': instance.creative.toJson(),
      'layouts': instance.layouts.map((e) => e.toJson()).toList(),
      'requested_users': instance.requestedUsers.toJson(),
      'lead_section_details': instance.leadSectionDetails.toJson(),
    };
