// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'badge_card.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BadgeCard _$BadgeCardFromJson(Map<String, dynamic> json) => BadgeCard(
      message: json['message'] as String? ?? '',
      badge: Badge.fromJson(json['badge'] as Map<String, dynamic>),
      invitedCount: (json['invited_count'] as num?)?.toInt() ?? 0,
      countToBeInvited: (json['count_to_be_invited'] as num?)?.toInt() ?? 10,
      circleId: (json['circle_id'] as num).toInt(),
      showDetails: json['show_details'] as bool? ?? true,
      backgroundGradients: CustomGradient.fromJson(
          json['background_gradients'] as Map<String, dynamic>),
      totalInvitedText: json['total_invited_text'] as String? ?? '',
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$BadgeCardToJson(BadgeCard instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['message'] = instance.message;
  val['badge'] = instance.badge.toJson();
  val['invited_count'] = instance.invitedCount;
  val['count_to_be_invited'] = instance.countToBeInvited;
  val['circle_id'] = instance.circleId;
  val['show_details'] = instance.showDetails;
  val['background_gradients'] = instance.backgroundGradients.toJson();
  val['total_invited_text'] = instance.totalInvitedText;
  return val;
}
