// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Poster _$PosterFromJson(Map<String, dynamic> json) => Poster(
      id: (json['id'] as num).toInt(),
      photoUrl: json['photo_url'] as String? ?? '',
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      cardText: ShareCardText.fromJson(
          json['share_card_text'] as Map<String, dynamic>),
      gradients:
          CustomGradient.fromJson(json['gradients'] as Map<String, dynamic>),
      leadersPhotos: (json['leaders_photo_urls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      leaderPhotoRingColor:
          (json['leader_photo_ring_color'] as num?)?.toInt() ?? 4294967295,
      retryButtonText:
          json['retry_button_text'] as String? ?? 'మళ్ళీ రీలోడ్ చేయండి',
      retryMsgText: json['retry_message_text'] as String? ??
          'మీ పోస్టర్ సరిగా లోడ్ కాలేదు, షేర్ చేయడానికి మళ్ళీ రీలోడ్ చేయండి.',
      posterVariant:
          $enumDecodeNullable(_$PosterVariantEnumMap, json['poster_variant']) ??
              PosterVariant.normal,
      frameUploadInfo: json['frame_upload_info'] as String? ?? '',
      frameUploadButtonText:
          json['frame_upload_button_text'] as String? ?? 'ఫోటో జోడించండి',
    );

Map<String, dynamic> _$PosterToJson(Poster instance) => <String, dynamic>{
      'id': instance.id,
      'photo_url': instance.photoUrl,
      'user': instance.user.toJson(),
      'share_card_text': instance.cardText.toJson(),
      'gradients': instance.gradients.toJson(),
      'leaders_photo_urls': instance.leadersPhotos,
      'leader_photo_ring_color': instance.leaderPhotoRingColor,
      'retry_button_text': instance.retryButtonText,
      'retry_message_text': instance.retryMsgText,
      'poster_variant': _$PosterVariantEnumMap[instance.posterVariant]!,
      'frame_upload_info': instance.frameUploadInfo,
      'frame_upload_button_text': instance.frameUploadButtonText,
    };

const _$PosterVariantEnumMap = {
  PosterVariant.normal: 'NORMAL',
  PosterVariant.landscapeFrame: 'LANDSCAPE_FRAME',
  PosterVariant.portraitFrame: 'PORTRAIT_FRAME',
  PosterVariant.none: 'NONE',
};
