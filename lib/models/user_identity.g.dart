// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_identity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserIdentity _$UserIdentityFromJson(Map<String, dynamic> json) => UserIdentity(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      photo: json['photo'] == null
          ? null
          : Photo.fromJson(json['photo'] as Map<String, dynamic>),
      badge: json['badge'] == null
          ? null
          : Badge.fromJson(json['badge'] as Map<String, dynamic>),
      follows: json['follows'] as bool? ?? false,
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
      location: json['location'] == null
          ? null
          : UserLocation.fromJson(json['location'] as Map<String, dynamic>),
      backingFieldAvatarColor: json['avatar_color'] as String?,
      isPremium: json['is_premium'] as bool? ?? false,
      showPremiumGoldIcon: json['show_premium_gold_icon'] as bool? ?? false,
    );

Map<String, dynamic> _$UserIdentityToJson(UserIdentity instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'name': instance.name,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('photo', instance.photo?.toJson());
  writeNotNull('badge', instance.badge?.toJson());
  val['follows'] = instance.follows;
  writeNotNull('analytics_params', instance.analyticsParams);
  writeNotNull('avatar_color', instance.backingFieldAvatarColor);
  writeNotNull('location', instance.location?.toJson());
  val['is_premium'] = instance.isPremium;
  val['show_premium_gold_icon'] = instance.showPremiumGoldIcon;
  return val;
}

UserLocation _$UserLocationFromJson(Map<String, dynamic> json) => UserLocation(
      name: json['name'] as String,
      id: (json['id'] as num).toInt(),
      level: json['level'] as String,
    );

Map<String, dynamic> _$UserLocationToJson(UserLocation instance) =>
    <String, dynamic>{
      'name': instance.name,
      'id': instance.id,
      'level': instance.level,
    };
