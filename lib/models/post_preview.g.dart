// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_preview.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostPreview _$PostPreviewFromJson(Map<String, dynamic> json) => PostPreview(
      id: (json['id'] as num).toInt(),
      content: json['content'] as String? ?? '',
      user: UserIdentity.fromJson(json['user'] as Map<String, dynamic>),
      photos: (json['photos'] as List<dynamic>?)
              ?.map((e) => Photo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      videos: (json['videos'] as List<dynamic>?)
              ?.map((e) => Video.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      linkPreview: json['link'] == null
          ? null
          : LinkPreview.fromJson(json['link'] as Map<String, dynamic>),
      parent: json['parent_post'] == null
          ? null
          : PostPreview.fromJson(json['parent_post'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PostPreviewToJson(PostPreview instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'content': instance.content,
    'user': instance.user.toJson(),
    'photos': instance.photos.map((e) => e.toJson()).toList(),
    'videos': instance.videos.map((e) => e.toJson()).toList(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('parent_post', instance.parent?.toJson());
  writeNotNull('link', instance.linkPreview?.toJson());
  return val;
}
