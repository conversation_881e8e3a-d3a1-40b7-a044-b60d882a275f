// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'circle_details.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CircleDetails _$CircleDetailsFromJson(Map<String, dynamic> json) =>
    CircleDetails(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      nameEN: json['name_en'] as String,
      level: json['level'] as String,
      hashId: json['hashid'] as String?,
      profilePhoto: json['profile_photo'] == null
          ? null
          : Photo.fromJson(json['profile_photo'] as Map<String, dynamic>),
      type: json['circle_type'] as String,
      description: json['description'] as String? ?? '',
      leaderPhotos: (json['leader_photos'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      circleBackground: json['circle_background'] == null
          ? null
          : CircleBackground.fromJson(
              json['circle_background'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CircleDetailsToJson(CircleDetails instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'name': instance.name,
    'name_en': instance.nameEN,
    'level': instance.level,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('hashid', instance.hashId);
  writeNotNull('profile_photo', instance.profilePhoto?.toJson());
  val['circle_type'] = instance.type;
  val['description'] = instance.description;
  val['leader_photos'] = instance.leaderPhotos;
  writeNotNull('circle_background', instance.circleBackground?.toJson());
  return val;
}
