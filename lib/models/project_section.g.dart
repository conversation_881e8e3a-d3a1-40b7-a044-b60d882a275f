// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project_section.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectSection _$ProjectSectionFromJson(Map<String, dynamic> json) =>
    ProjectSection(
      headerText: json['header'] as String,
      gradients:
          CustomGradient.fromJson(json['gradients'] as Map<String, dynamic>),
      projects: (json['projects'] as List<dynamic>)
          .map((e) => Project.fromJson(e as Map<String, dynamic>))
          .toList(),
      bottomLineColor:
          (json['bottom_line_color'] as num?)?.toInt() ?? 4294244607,
      shareText: json['share_text'] as String,
      buttonsTextColor:
          (json['buttons_text_color'] as num?)?.toInt() ?? 4294967295,
    );

Map<String, dynamic> _$ProjectSectionToJson(ProjectSection instance) =>
    <String, dynamic>{
      'header': instance.headerText,
      'gradients': instance.gradients.toJson(),
      'projects': instance.projects.map((e) => e.toJson()).toList(),
      'bottom_line_color': instance.bottomLineColor,
      'buttons_text_color': instance.buttonsTextColor,
      'share_text': instance.shareText,
    };
