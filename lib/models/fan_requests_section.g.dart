// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fan_requests_section.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FanRequestsSection _$FanRequestsSectionFromJson(Map<String, dynamic> json) =>
    FanRequestsSection(
      circleDetails: CircleDetails.fromJson(
          json['circle_details'] as Map<String, dynamic>),
      users: (json['users'] as List<dynamic>?)
              ?.map((e) => UserIdentity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      additionalUsersCount: (json['users_count'] as num?)?.toInt() ?? 0,
      header: json['header'] as String? ?? '',
      ctaText: json['cta_text'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$FanRequestsSectionToJson(FanRequestsSection instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['circle_details'] = instance.circleDetails.toJson();
  val['users'] = instance.users.map((e) => e.toJson()).toList();
  val['users_count'] = instance.additionalUsersCount;
  val['header'] = instance.header;
  val['cta_text'] = instance.ctaText;
  writeNotNull('analytics_params', instance.analyticsParams);
  return val;
}
