// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'about_my_leader.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AboutMyLeader _$AboutMyLeaderFromJson(Map<String, dynamic> json) =>
    AboutMyLeader(
      headerText: json['header'] as String? ?? 'లీడర్ గురించి',
      backgroundColor:
          (json['background_color'] as num?)?.toInt() ?? 4294244607,
      outlineColor: (json['outline_color'] as num?)?.toInt() ?? 4283010543,
      leader: User.fromJson(json['user'] as Map<String, dynamic>),
      partyCircle: json['party_circle'] == null
          ? null
          : Circle.fromJson(json['party_circle'] as Map<String, dynamic>),
      details: (json['details'] as List<dynamic>?)
          ?.map((e) => LeaderDetails.fromJson(e as Map<String, dynamic>))
          .toList(),
      independentText: json['independent_text'] as String?,
      shareText: json['share_text'] as String,
    );

Map<String, dynamic> _$AboutMyLeaderToJson(AboutMyLeader instance) {
  final val = <String, dynamic>{
    'header': instance.headerText,
    'background_color': instance.backgroundColor,
    'outline_color': instance.outlineColor,
    'user': instance.leader.toJson(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('party_circle', instance.partyCircle?.toJson());
  writeNotNull('details', instance.details?.map((e) => e.toJson()).toList());
  writeNotNull('independent_text', instance.independentText);
  val['share_text'] = instance.shareText;
  return val;
}
