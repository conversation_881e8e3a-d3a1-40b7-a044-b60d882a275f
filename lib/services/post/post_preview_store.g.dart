// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_preview_store.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PostPreviewValueAdapter extends TypeAdapter<PostPreviewValue> {
  @override
  final int typeId = 2;

  @override
  PostPreviewValue read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PostPreviewValue(
      postPreviewSerialized: fields[0] as String,
      updatedAt: fields[1] as int,
    );
  }

  @override
  void write(BinaryWriter writer, PostPreviewValue obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.postPreviewSerialized)
      ..writeByte(1)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PostPreviewValueAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
