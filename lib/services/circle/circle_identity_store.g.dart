// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'circle_identity_store.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CircleIdentityValueAdapter extends TypeAdapter<CircleIdentityValue> {
  @override
  final int typeId = 4;

  @override
  CircleIdentityValue read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CircleIdentityValue(
      circleIdentitySerialized: fields[0] as String,
      updatedAt: fields[1] as int,
    );
  }

  @override
  void write(BinaryWriter writer, CircleIdentityValue obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.circleIdentitySerialized)
      ..writeByte(1)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CircleIdentityValueAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
