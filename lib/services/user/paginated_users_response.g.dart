// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paginated_users_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaginatedUsersResponse _$PaginatedUsersResponseFromJson(
        Map<String, dynamic> json) =>
    PaginatedUsersResponse(
      users: (json['users'] as List<dynamic>?)
              ?.map((e) => UserIdentity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      nextPageQueryParams:
          json['next_page_query_params'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaginatedUsersResponseToJson(
    PaginatedUsersResponse instance) {
  final val = <String, dynamic>{
    'users': instance.users.map((e) => e.toJson()).toList(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('next_page_query_params', instance.nextPageQueryParams);
  return val;
}
