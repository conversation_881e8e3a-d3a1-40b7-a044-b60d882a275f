// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recharge_pay_wall_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RechargePaywallResponse _$RechargePaywallResponseFromJson(
        Map<String, dynamic> json) =>
    RechargePaywallResponse(
      title: json['title'] as String? ?? '',
      planId: (json['plan_id'] as num).toInt(),
      payWall: (json['pay_wall'] as List<dynamic>?)
              ?.map((e) => RechargePayBlock.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      rmUser: json['rm_user'] == null
          ? null
          : AdminUser.fromJson(json['rm_user'] as Map<String, dynamic>),
      help: json['help'] == null
          ? null
          : PaywallHelp.fromJson(json['help'] as Map<String, dynamic>),
      autoPayCancelText: json['auto_pay_cancel_text'] as String? ?? '',
      existingPremiumUsers: json['existing_premium_users'] == null
          ? null
          : ExistingPremiumUsers.fromJson(
              json['existing_premium_users'] as Map<String, dynamic>),
      buttonDetails: PremiumExperienceButtonDetails.fromJson(
          json['button_details'] as Map<String, dynamic>),
      terms: json['terms'] == null
          ? null
          : PosterTerms.fromJson(json['terms'] as Map<String, dynamic>),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
      paymentGateway: $enumDecodeNullable(
              _$PaymentGatewayEnumEnumMap, json['payment_gateway'],
              unknownValue: PaymentGatewayEnum.url) ??
          PaymentGatewayEnum.url,
    );

Map<String, dynamic> _$RechargePaywallResponseToJson(
    RechargePaywallResponse instance) {
  final val = <String, dynamic>{
    'title': instance.title,
    'plan_id': instance.planId,
    'pay_wall': instance.payWall.map((e) => e.toJson()).toList(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('rm_user', instance.rmUser?.toJson());
  writeNotNull('help', instance.help?.toJson());
  val['auto_pay_cancel_text'] = instance.autoPayCancelText;
  writeNotNull(
      'existing_premium_users', instance.existingPremiumUsers?.toJson());
  val['button_details'] = instance.buttonDetails.toJson();
  writeNotNull('terms', instance.terms?.toJson());
  writeNotNull('analytics_params', instance.analyticsParams);
  val['payment_gateway'] =
      _$PaymentGatewayEnumEnumMap[instance.paymentGateway]!;
  return val;
}

const _$PaymentGatewayEnumEnumMap = {
  PaymentGatewayEnum.url: 'url',
  PaymentGatewayEnum.juspay: 'juspay',
  PaymentGatewayEnum.intent: 'intent',
};

RechargePayBlock _$RechargePayBlockFromJson(Map<String, dynamic> json) =>
    RechargePayBlock(
      type: $enumDecodeNullable(_$RechargePayBlockTypeEnumMap, json['type'],
              unknownValue: RechargePayBlockType.text) ??
          RechargePayBlockType.text,
      imageUrl: json['image_url'] as String? ?? '',
      text: json['text'] as String? ?? '',
      subtext: json['sub_text'] as String? ?? '',
      defineText: json['define_text'] as String? ?? '',
    );

Map<String, dynamic> _$RechargePayBlockToJson(RechargePayBlock instance) =>
    <String, dynamic>{
      'type': _$RechargePayBlockTypeEnumMap[instance.type]!,
      'image_url': instance.imageUrl,
      'text': instance.text,
      'sub_text': instance.subtext,
      'define_text': instance.defineText,
    };

const _$RechargePayBlockTypeEnumMap = {
  RechargePayBlockType.image: 'image',
  RechargePayBlockType.text: 'text',
  RechargePayBlockType.icon: 'icon',
};

PaywallHelp _$PaywallHelpFromJson(Map<String, dynamic> json) => PaywallHelp(
      title: json['title'] as String? ?? '',
      rmOptionText: json['rm_option_text'] as String? ?? '',
      paymentShareText: json['payment_share_text'] as String? ?? '',
    );

Map<String, dynamic> _$PaywallHelpToJson(PaywallHelp instance) =>
    <String, dynamic>{
      'title': instance.title,
      'rm_option_text': instance.rmOptionText,
      'payment_share_text': instance.paymentShareText,
    };
