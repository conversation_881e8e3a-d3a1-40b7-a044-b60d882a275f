// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'usage_counts.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UsageCounts _$UsageCountsFromJson(Map<String, dynamic> json) => UsageCounts(
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => UsageCountItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      title: json['title'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$UsageCountsToJson(UsageCounts instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['items'] = instance.items.map((e) => e.toJson()).toList();
  val['title'] = instance.title;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}

UsageCountItem _$UsageCountItemFromJson(Map<String, dynamic> json) =>
    UsageCountItem(
      count: (json['count'] as num?)?.toInt() ?? 0,
      text: json['text'] as String? ?? '',
    );

Map<String, dynamic> _$UsageCountItemToJson(UsageCountItem instance) =>
    <String, dynamic>{
      'count': instance.count,
      'text': instance.text,
    };
