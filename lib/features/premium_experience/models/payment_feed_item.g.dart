// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_feed_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentFeedItem _$PaymentFeedItemFromJson(Map<String, dynamic> json) =>
    PaymentFeedItem(
      text: json['text'] as String? ?? '',
      subText: json['sub_text'] as String? ?? '',
      discountText: json['discount_text'] as String? ?? '',
      discountTextColor:
          (json['discount_text_color'] as num?)?.toInt() ?? 4283344130,
      deeplink: json['deeplink'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$PaymentFeedItemToJson(PaymentFeedItem instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['text'] = instance.text;
  val['sub_text'] = instance.subText;
  val['discount_text'] = instance.discountText;
  val['discount_text_color'] = instance.discountTextColor;
  val['deeplink'] = instance.deeplink;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}
