// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'premium_members.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PremiumMembers _$PremiumMembersFromJson(Map<String, dynamic> json) =>
    PremiumMembers(
      members: (json['members'] as List<dynamic>?)
              ?.map((e) => UserIdentity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      title: json['title'] as String? ?? '',
      locked: json['locked'] as bool? ?? false,
      additionalUsersCount:
          (json['additional_users_count'] as num?)?.toInt() ?? 0,
      deeplink: json['deeplink'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$PremiumMembersToJson(PremiumMembers instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['members'] = instance.members.map((e) => e.toJson()).toList();
  val['title'] = instance.title;
  val['locked'] = instance.locked;
  val['additional_users_count'] = instance.additionalUsersCount;
  val['deeplink'] = instance.deeplink;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}
