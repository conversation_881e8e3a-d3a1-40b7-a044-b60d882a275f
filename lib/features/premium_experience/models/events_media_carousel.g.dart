// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'events_media_carousel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EventsMediaCarousel _$EventsMediaCarouselFromJson(Map<String, dynamic> json) =>
    EventsMediaCarousel(
      title: json['title'] as String? ?? '',
      media: (json['media'] as List<dynamic>?)
              ?.map((e) => EventsMediaItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      locked: json['locked'] as bool? ?? false,
      deeplink: json['deeplink'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$EventsMediaCarouselToJson(EventsMediaCarousel instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['title'] = instance.title;
  val['media'] = instance.media.map((e) => e.toJson()).toList();
  val['locked'] = instance.locked;
  val['deeplink'] = instance.deeplink;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}

EventsMediaItem _$EventsMediaItemFromJson(Map<String, dynamic> json) =>
    EventsMediaItem(
      type: $enumDecodeNullable(_$EventsMediaTypeEnumMap, json['type'],
              unknownValue: EventsMediaType.image) ??
          EventsMediaType.image,
      url: json['url'] as String? ?? '',
      creative: json['creative'] == null
          ? null
          : PosterCreative.fromJson(json['creative'] as Map<String, dynamic>),
      layout: json['layout'] == null
          ? null
          : PosterLayout.fromJson(json['layout'] as Map<String, dynamic>),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
    );

Map<String, dynamic> _$EventsMediaItemToJson(EventsMediaItem instance) {
  final val = <String, dynamic>{
    'type': _$EventsMediaTypeEnumMap[instance.type]!,
    'url': instance.url,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('creative', instance.creative?.toJson());
  writeNotNull('layout', instance.layout?.toJson());
  val['analytics_params'] = instance.analyticsParams;
  return val;
}

const _$EventsMediaTypeEnumMap = {
  EventsMediaType.image: 'image',
  EventsMediaType.poster: 'poster',
};
