// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'premium_experience_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PremiumExperienceResponse _$PremiumExperienceResponseFromJson(
        Map<String, dynamic> json) =>
    PremiumExperienceResponse(
      feedItems: (json['feed_items'] as List<dynamic>?)
              ?.map((e) => FeedItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      buttonDetails: PremiumExperienceButtonDetails.fromJson(
          json['button_details'] as Map<String, dynamic>),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
    );

Map<String, dynamic> _$PremiumExperienceResponseToJson(
        PremiumExperienceResponse instance) =>
    <String, dynamic>{
      'feed_items': instance.feedItems.map((e) => e.toJson()).toList(),
      'button_details': instance.buttonDetails.toJson(),
      'analytics_params': instance.analyticsParams,
    };
