// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_poster_styles.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MyPosterStyles _$MyPosterStylesFromJson(Map<String, dynamic> json) =>
    MyPosterStyles(
      title: json['title'] as String? ?? '',
      locked: json['locked'] as bool? ?? false,
      creative:
          PosterCreative.fromJson(json['creative'] as Map<String, dynamic>),
      layouts: (json['layouts'] as List<dynamic>?)
              ?.map((e) => PosterLayout.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      deeplink: json['deeplink'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$MyPosterStylesToJson(MyPosterStyles instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['title'] = instance.title;
  val['locked'] = instance.locked;
  val['creative'] = instance.creative.toJson();
  val['layouts'] = instance.layouts.map((e) => e.toJson()).toList();
  val['deeplink'] = instance.deeplink;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}
