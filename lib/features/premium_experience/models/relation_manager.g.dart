// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'relation_manager.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RelationManager _$RelationManagerFromJson(Map<String, dynamic> json) =>
    RelationManager(
      title: json['title'] as String? ?? '',
      rmNameTextColor:
          (json['rm_name_text_color'] as num?)?.toInt() ?? 4287598479,
      rmUser: AdminUser.fromJson(json['rm_user'] as Map<String, dynamic>),
      showActionButtons: json['show_action_buttons'] as bool? ?? false,
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$RelationManagerToJson(RelationManager instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['title'] = instance.title;
  val['rm_name_text_color'] = instance.rmNameTextColor;
  val['rm_user'] = instance.rmUser.toJson();
  val['show_action_buttons'] = instance.showActionButtons;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}
