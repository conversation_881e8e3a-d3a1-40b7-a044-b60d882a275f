// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'string_store.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PluralizedStringAdapter extends TypeAdapter<PluralizedString> {
  @override
  final int typeId = 6;

  @override
  PluralizedString read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PluralizedString(
      zero: fields[0] as String?,
      one: fields[1] as String?,
      other: fields[2] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PluralizedString obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.zero)
      ..writeByte(1)
      ..write(obj.one)
      ..writeByte(2)
      ..write(obj.other);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluralizedStringAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PluralizedString _$PluralizedStringFromJson(Map<String, dynamic> json) =>
    PluralizedString(
      zero: json['zero'] as String?,
      one: json['one'] as String?,
      other: json['other'] as String?,
    );

Map<String, dynamic> _$PluralizedStringToJson(PluralizedString instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('zero', instance.zero);
  writeNotNull('one', instance.one);
  writeNotNull('other', instance.other);
  return val;
}
