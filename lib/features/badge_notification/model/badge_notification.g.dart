// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'badge_notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BadgeNotification _$BadgeNotificationFromJson(Map<String, dynamic> json) =>
    BadgeNotification(
      header: json['header'] as String,
      inviteCardHeader: json['invite_card_header'] as String,
      badgeRoleHeader: json['badge_role_header'] as String,
      uploadPictureText: json['upload_picture_text'] as String,
      subHeader: json['sub_header'] as String,
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      footer: json['footer'] as String,
    );

Map<String, dynamic> _$BadgeNotificationToJson(BadgeNotification instance) =>
    <String, dynamic>{
      'header': instance.header,
      'invite_card_header': instance.inviteCardHeader,
      'badge_role_header': instance.badgeRoleHeader,
      'sub_header': instance.subHeader,
      'upload_picture_text': instance.uploadPictureText,
      'user': instance.user.toJson(),
      'footer': instance.footer,
    };
