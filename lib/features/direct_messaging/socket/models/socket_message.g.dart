// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'socket_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SocketMessage _$SocketMessageFromJson(Map<String, dynamic> json) =>
    SocketMessage(
      id: json['id'] as String,
      conversationId: json['conversationId'] as String,
      messageData: NormalMessageData.fromJson(
          json['messageData'] as Map<String, dynamic>),
      sentAt: const UtcDateTimeConverter().fromJson(json['sentAt'] as String),
    );

Map<String, dynamic> _$SocketMessageToJson(SocketMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'conversationId': instance.conversationId,
      'messageData': instance.messageData.toJson(),
      'sentAt': const UtcDateTimeConverter().toJson(instance.sentAt),
    };
