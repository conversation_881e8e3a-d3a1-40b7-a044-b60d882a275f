// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'socket_delete_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SocketDeleteMessage _$SocketDeleteMessageFromJson(Map<String, dynamic> json) =>
    SocketDeleteMessage(
      id: json['id'] as String,
      conversationId: json['conversationId'] as String,
    );

Map<String, dynamic> _$SocketDeleteMessageToJson(
        SocketDeleteMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'conversationId': instance.conversationId,
    };
