// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_socket_events.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageStatusUpdated _$MessageStatusUpdatedFromJson(
        Map<String, dynamic> json) =>
    MessageStatusUpdated(
      messageId: json['messageId'] as String,
      updatedAt:
          const UtcDateTimeConverter().fromJson(json['updatedAt'] as String),
      deliveryStatus: json['deliveryStatus'] as String,
    );

Map<String, dynamic> _$MessageStatusUpdatedToJson(
        MessageStatusUpdated instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'updatedAt': const UtcDateTimeConverter().toJson(instance.updatedAt),
      'deliveryStatus': instance.deliveryStatus,
    };

UserDmCircles _$UserDmCirclesFromJson(Map<String, dynamic> json) =>
    UserDmCircles(
      supportedCircleConversationTypesMap:
          (json['supportedConversationTypesMap'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    k, (e as List<dynamic>).map((e) => e as String).toList()),
              ) ??
              {},
    );

Map<String, dynamic> _$UserDmCirclesToJson(UserDmCircles instance) =>
    <String, dynamic>{
      'supportedConversationTypesMap':
          instance.supportedCircleConversationTypesMap,
    };

MessageDelivered _$MessageDeliveredFromJson(Map<String, dynamic> json) =>
    MessageDelivered(
      messageId: json['messageId'] as String,
      messageSenderId: json['messageSenderId'] as String,
      userId: json['userId'] as String,
      deliveredAt:
          const UtcDateTimeConverter().fromJson(json['deliveredAt'] as String),
    );

Map<String, dynamic> _$MessageDeliveredToJson(MessageDelivered instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'messageSenderId': instance.messageSenderId,
      'userId': instance.userId,
      'deliveredAt': const UtcDateTimeConverter().toJson(instance.deliveredAt),
    };

MessageRead _$MessageReadFromJson(Map<String, dynamic> json) => MessageRead(
      messageId: json['messageId'] as String,
      messageSenderId: json['messageSenderId'] as String,
      userId: json['userId'] as String,
      readAt: const UtcDateTimeConverter().fromJson(json['readAt'] as String),
    );

Map<String, dynamic> _$MessageReadToJson(MessageRead instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'messageSenderId': instance.messageSenderId,
      'userId': instance.userId,
      'readAt': const UtcDateTimeConverter().toJson(instance.readAt),
    };

MessageReadHidden _$MessageReadHiddenFromJson(Map<String, dynamic> json) =>
    MessageReadHidden(
      messageId: json['messageId'] as String,
      messageSenderId: json['messageSenderId'] as String,
      userId: json['userId'] as String,
      readHiddenAt:
          const UtcDateTimeConverter().fromJson(json['readHiddenAt'] as String),
    );

Map<String, dynamic> _$MessageReadHiddenToJson(MessageReadHidden instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'messageSenderId': instance.messageSenderId,
      'userId': instance.userId,
      'readHiddenAt':
          const UtcDateTimeConverter().toJson(instance.readHiddenAt),
    };

MessageDeleted _$MessageDeletedFromJson(Map<String, dynamic> json) =>
    MessageDeleted(
      SocketDeleteMessage.fromJson(json['message'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MessageDeletedToJson(MessageDeleted instance) =>
    <String, dynamic>{
      'message': instance.message.toJson(),
    };
