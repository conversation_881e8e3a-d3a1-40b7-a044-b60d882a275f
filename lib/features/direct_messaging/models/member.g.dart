// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'member.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Member _$<PERSON>(Map<String, dynamic> json) => Member(
      label: json['label'] as String? ?? '',
      type: $enumDecodeNullable(_$MemberTypeEnumMap, json['type'],
              unknownValue: MemberType.member) ??
          MemberType.member,
      user: UserIdentity.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MemberTo<PERSON>son(Member instance) => <String, dynamic>{
      'label': instance.label,
      'type': _$MemberTypeEnumMap[instance.type]!,
      'user': instance.user.toJson(),
    };

const _$MemberTypeEnumMap = {
  MemberType.member: 'member',
  MemberType.admin: 'admin',
  MemberType.owner: 'owner',
  MemberType.passive: 'passive',
};
