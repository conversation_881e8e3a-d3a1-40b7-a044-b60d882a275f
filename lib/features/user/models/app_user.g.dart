// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppUser _$AppUserFromJson(Map<String, dynamic> json) => AppUser(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String?,
      phone: (json['phone'] as num).toInt(),
      otp: json['otp'] as String? ?? '',
      village: json['village'] == null
          ? null
          : Circle.fromJson(json['village'] as Map<String, dynamic>),
      mandal: json['mandal'] == null
          ? null
          : Circle.fromJson(json['mandal'] as Map<String, dynamic>),
      district: json['district'] == null
          ? null
          : Circle.fromJson(json['district'] as Map<String, dynamic>),
      shortBio: json['short_bio'] as String? ?? '',
      dob: json['dob'] == null ? null : DateTime.parse(json['dob'] as String),
      gender: (json['gender_new'] as num?)?.toInt(),
      badge: json['badge'] == null
          ? null
          : Badge.fromJson(json['badge'] as Map<String, dynamic>),
      verified: json['verified'] as bool? ?? false,
      internal: json['internal'] as bool? ?? false,
      photo: json['photo'] == null
          ? null
          : Photo.fromJson(json['photo'] as Map<String, dynamic>),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      isFeedExperimentUser: json['is_feed_experiment_user'] as bool? ?? false,
      showPartySuggestions: json['show_party_suggestions'] as bool? ?? false,
      skipPartyMemberDecision:
          json['skip_party_member_decision'] as bool? ?? true,
      exclusivePartyJoin: json['exclusive_party_join'] as bool? ?? false,
      referralPoints: (json['referral_points'] as num?)?.toInt() ?? 0,
      hasProfilePicture: json['has_profile_picture'] as bool? ?? false,
      followersCount: (json['followers_count'] as num?)?.toInt() ?? 0,
      followingCount: (json['following_count'] as num?)?.toInt() ?? 0,
      postsCount: (json['posts_count'] as num?)?.toInt() ?? 0,
      avatarColor: json['avatar_color'] as String? ?? '#3F51B5',
      isPremium: json['is_premium'] as bool? ?? false,
      showPremiumGoldIcon: json['show_premium_gold_icon'] as bool? ?? false,
      eligibleForPremium: json['eligible_for_premium'] as bool? ?? false,
      showSupport: json['show_support'] as bool? ?? true,
    );

Map<String, dynamic> _$AppUserToJson(AppUser instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'name': instance.name,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('email', instance.email);
  val['phone'] = instance.phone;
  val['otp'] = instance.otp;
  writeNotNull('village', instance.village?.toJson());
  writeNotNull('mandal', instance.mandal?.toJson());
  writeNotNull('district', instance.district?.toJson());
  val['short_bio'] = instance.shortBio;
  writeNotNull('dob', instance.dob?.toIso8601String());
  writeNotNull('gender_new', instance.gender);
  writeNotNull('badge', instance.badge?.toJson());
  val['verified'] = instance.verified;
  val['internal'] = instance.internal;
  writeNotNull('photo', instance.photo?.toJson());
  writeNotNull('created_at', instance.createdAt?.toIso8601String());
  val['is_feed_experiment_user'] = instance.isFeedExperimentUser;
  val['show_party_suggestions'] = instance.showPartySuggestions;
  val['skip_party_member_decision'] = instance.skipPartyMemberDecision;
  val['exclusive_party_join'] = instance.exclusivePartyJoin;
  val['referral_points'] = instance.referralPoints;
  val['has_profile_picture'] = instance.hasProfilePicture;
  val['followers_count'] = instance.followersCount;
  val['following_count'] = instance.followingCount;
  val['posts_count'] = instance.postsCount;
  val['avatar_color'] = instance.avatarColor;
  val['is_premium'] = instance.isPremium;
  val['show_premium_gold_icon'] = instance.showPremiumGoldIcon;
  val['eligible_for_premium'] = instance.eligibleForPremium;
  val['show_support'] = instance.showSupport;
  return val;
}
