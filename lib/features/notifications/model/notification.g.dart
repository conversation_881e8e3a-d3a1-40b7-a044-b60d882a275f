// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationElement _$NotificationElementFromJson(Map<String, dynamic> json) =>
    NotificationElement(
      id: (json['id'] as num).toInt(),
      description: json['description'] as String,
      entityType: json['entity_type'] as String,
      entityId: (json['entity_id'] as num).toInt(),
      entity: json['entity'] == null
          ? null
          : User.fromJson(json['entity'] as Map<String, dynamic>),
      read: json['read'] as bool? ?? false,
      notificationType: json['notification_type'] as String,
      notificationIconUrl: json['notification_icon'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      userId: (json['user_id'] as num).toInt(),
      delivered: json['delivered'] as bool? ?? false,
      received: json['received'] as bool? ?? false,
      active: json['active'] as bool? ?? false,
      deepLink: json['deep_link'] as String?,
    );

Map<String, dynamic> _$NotificationElementToJson(NotificationElement instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'description': instance.description,
    'entity_type': instance.entityType,
    'entity_id': instance.entityId,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('entity', instance.entity?.toJson());
  val['notification_type'] = instance.notificationType;
  writeNotNull('notification_icon', instance.notificationIconUrl);
  val['read'] = instance.read;
  val['created_at'] = instance.createdAt.toIso8601String();
  val['updated_at'] = instance.updatedAt.toIso8601String();
  val['user_id'] = instance.userId;
  val['delivered'] = instance.delivered;
  val['received'] = instance.received;
  val['active'] = instance.active;
  writeNotNull('deep_link', instance.deepLink);
  return val;
}
