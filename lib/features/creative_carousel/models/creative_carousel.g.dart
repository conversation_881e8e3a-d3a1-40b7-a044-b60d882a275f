// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'creative_carousel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreativeCarousel _$CreativeCarouselFromJson(Map<String, dynamic> json) =>
    CreativeCarousel(
      title: json['title'] as String? ?? '',
      subtitle: json['subtitle'] as String? ?? '',
      icon: json['icon'] == null
          ? null
          : CreativeCarouselIcon.fromJson(json['icon'] as Map<String, dynamic>),
      creativeWidth: (json['creative_width'] as num?)?.toInt() ?? 400,
      creativeHeight: (json['creative_height'] as num?)?.toInt() ?? 500,
      itemToCarouselWidthRatio:
          (json['item_to_carousel_width_ratio'] as num?)?.toDouble() ?? 0.92,
      items: (json['items'] as List<dynamic>)
          .map((e) => CreativeCarouselItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      ctaText: json['cta_text'] as String? ?? '',
      ctaDeeplink: json['cta_deeplink'] as String? ?? '',
      ctaDescription: json['cta_description'] as String? ?? '',
      nextPageUrl: json['next_page_url'] as String?,
      liveConfig: json['live_config'] == null
          ? null
          : LiveConfig.fromJson(json['live_config'] as Map<String, dynamic>),
      disableScreenshot: json['disable_screenshot'] as bool? ?? true,
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$CreativeCarouselToJson(CreativeCarousel instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['title'] = instance.title;
  val['subtitle'] = instance.subtitle;
  writeNotNull('icon', instance.icon?.toJson());
  val['creative_width'] = instance.creativeWidth;
  val['creative_height'] = instance.creativeHeight;
  val['item_to_carousel_width_ratio'] = instance.itemToCarouselWidthRatio;
  val['items'] = instance.items.map((e) => e.toJson()).toList();
  val['cta_text'] = instance.ctaText;
  val['cta_deeplink'] = instance.ctaDeeplink;
  val['cta_description'] = instance.ctaDescription;
  writeNotNull('next_page_url', instance.nextPageUrl);
  writeNotNull('live_config', instance.liveConfig?.toJson());
  val['disable_screenshot'] = instance.disableScreenshot;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}

CreativeCarouselIcon _$CreativeCarouselIconFromJson(
        Map<String, dynamic> json) =>
    CreativeCarouselIcon(
      url: json['url'] as String? ?? '',
      type: $enumDecodeNullable(_$CreativeCarouselIconTypeEnumMap, json['type'],
              unknownValue: CreativeCarouselIconType.circle) ??
          CreativeCarouselIconType.circle,
    );

Map<String, dynamic> _$CreativeCarouselIconToJson(
        CreativeCarouselIcon instance) =>
    <String, dynamic>{
      'url': instance.url,
      'type': _$CreativeCarouselIconTypeEnumMap[instance.type]!,
    };

const _$CreativeCarouselIconTypeEnumMap = {
  CreativeCarouselIconType.user: 'user',
  CreativeCarouselIconType.circle: 'circle',
};

CreativeCarouselNextPage _$CreativeCarouselNextPageFromJson(
        Map<String, dynamic> json) =>
    CreativeCarouselNextPage(
      nextPageUrl: json['next_page_url'] as String?,
      items: (json['items'] as List<dynamic>)
          .map((e) => CreativeCarouselItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CreativeCarouselNextPageToJson(
    CreativeCarouselNextPage instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('next_page_url', instance.nextPageUrl);
  val['items'] = instance.items.map((e) => e.toJson()).toList();
  return val;
}
