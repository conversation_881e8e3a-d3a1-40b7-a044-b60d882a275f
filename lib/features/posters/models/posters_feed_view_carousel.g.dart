// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'posters_feed_view_carousel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostersFeedViewCarousel _$PostersFeedViewCarouselFromJson(
        Map<String, dynamic> json) =>
    PostersFeedViewCarousel(
      layouts: (json['layouts'] as List<dynamic>)
          .map((e) => PosterLayout.fromJson(e as Map<String, dynamic>))
          .toList(),
      creatives: (json['creatives'] as List<dynamic>)
          .map((e) => PosterCreative.fromJson(e as Map<String, dynamic>))
          .toList(),
      showHelp: json['show_help'] as bool? ?? false,
      disableScreenshot: json['disable_screenshot'] as bool? ?? true,
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$PostersFeedViewCarouselToJson(
    PostersFeedViewCarousel instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['layouts'] = instance.layouts.map((e) => e.toJson()).toList();
  val['creatives'] = instance.creatives.map((e) => e.toJson()).toList();
  val['show_help'] = instance.showHelp;
  val['disable_screenshot'] = instance.disableScreenshot;
  writeNotNull('analytics_params', instance.analyticsParams);
  return val;
}
