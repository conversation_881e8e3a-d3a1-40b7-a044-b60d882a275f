// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'posters_feed_view_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostersFeedViewResponse _$PostersFeedViewResponseFromJson(
        Map<String, dynamic> json) =>
    PostersFeedViewResponse(
      feedItems: (json['feed_items'] as List<dynamic>?)
              ?.map((e) => FeedItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PostersFeedViewResponseToJson(
    PostersFeedViewResponse instance) {
  final val = <String, dynamic>{
    'feed_items': instance.feedItems.map((e) => e.toJson()).toList(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('analytics_params', instance.analyticsParams);
  return val;
}
