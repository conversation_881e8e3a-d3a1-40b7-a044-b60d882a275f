// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_banner.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubscriptionBanner _$SubscriptionBannerFromJson(Map<String, dynamic> json) =>
    SubscriptionBanner(
      imageUrl: json['image_url'] as String,
      subscribeInfoText: json['subscribe_info_text'] as String,
      subscribeBannerText: json['subscribe_banner_text'] as String,
      subscriptionScreen: json['subscription_screen'] == null
          ? null
          : PosterSubscriptionScreen.fromJson(
              json['subscription_screen'] as Map<String, dynamic>),
      feedType: json['feed_type'] as String,
    );

Map<String, dynamic> _$SubscriptionBannerToJson(SubscriptionBanner instance) {
  final val = <String, dynamic>{
    'feed_type': instance.feedType,
    'image_url': instance.imageUrl,
    'subscribe_info_text': instance.subscribeInfoText,
    'subscribe_banner_text': instance.subscribeBannerText,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('subscription_screen', instance.subscriptionScreen?.toJson());
  return val;
}
