// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_search_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserSearchItem _$UserSearchItemFromJson(Map<String, dynamic> json) =>
    UserSearchItem(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      phone: (json['phone'] as num).toInt(),
      photo: json['photo'] == null
          ? null
          : Photo.fromJson(json['photo'] as Map<String, dynamic>),
      village: json['village'] == null
          ? null
          : Circle.fromJson(json['village'] as Map<String, dynamic>),
      followersCount: (json['followers_count'] as num).toInt(),
      loggedInUser: json['loggedInUser'] as bool,
      follows: json['follows'] as bool,
      badge: json['badge'] == null
          ? null
          : Badge.fromJson(json['badge'] as Map<String, dynamic>),
      avatarColor: json['avatar_color'] as String,
    );

Map<String, dynamic> _$UserSearchItemToJson(UserSearchItem instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'name': instance.name,
    'phone': instance.phone,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('photo', instance.photo?.toJson());
  writeNotNull('village', instance.village?.toJson());
  val['followers_count'] = instance.followersCount;
  val['loggedInUser'] = instance.loggedInUser;
  val['follows'] = instance.follows;
  writeNotNull('badge', instance.badge?.toJson());
  val['avatar_color'] = instance.avatarColor;
  return val;
}
