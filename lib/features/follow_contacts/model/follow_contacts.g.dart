// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follow_contacts.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FollowContacts _$FollowContactsFromJson(Map<String, dynamic> json) =>
    FollowContacts(
      users: (json['users'] as List<dynamic>)
          .map((e) => User.fromJson(e as Map<String, dynamic>))
          .toList(),
      headerText: json['header_text'] as String,
      subHeaderText: json['sub_header_text'] as String,
    );

Map<String, dynamic> _$FollowContactsToJson(FollowContacts instance) =>
    <String, dynamic>{
      'users': instance.users.map((e) => e.toJson()).toList(),
      'header_text': instance.headerText,
      'sub_header_text': instance.subHeaderText,
    };
