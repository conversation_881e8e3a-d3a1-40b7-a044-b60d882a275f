// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'suggested_section.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SuggestedSection _$SuggestedSectionFromJson(Map<String, dynamic> json) =>
    SuggestedSection(
      headerText: json['header'] as String,
      subHeaderText: json['sub_header'] as String,
      usersCount: (json['users_count'] as num?)?.toInt() ?? 0,
      users: (json['users'] as List<dynamic>?)
              ?.map((e) => UserIdentity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      circles: (json['circles'] as List<dynamic>?)
              ?.map((e) => Circle.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      suggestedType: $enumDecodeNullable(
              _$SuggestedTypeEnumMap, json['suggested_type'],
              unknownValue: SuggestedType.users) ??
          SuggestedType.users,
      suggestedSubType: json['suggested_sub_type'] as String?,
      customProperties: json['custom_properties'] as Map<String, dynamic>?,
      showMoreSuggestions: json['show_more_suggestions'] as bool? ?? true,
      showMoreDeeplink:
          json['show_more_deeplink'] as String? ?? '/circles/suggested',
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$SuggestedSectionToJson(SuggestedSection instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['header'] = instance.headerText;
  val['sub_header'] = instance.subHeaderText;
  val['users_count'] = instance.usersCount;
  val['users'] = instance.users.map((e) => e.toJson()).toList();
  val['circles'] = instance.circles.map((e) => e.toJson()).toList();
  val['suggested_type'] = _$SuggestedTypeEnumMap[instance.suggestedType]!;
  writeNotNull('suggested_sub_type', instance.suggestedSubType);
  writeNotNull('custom_properties', instance.customProperties);
  val['show_more_suggestions'] = instance.showMoreSuggestions;
  val['show_more_deeplink'] = instance.showMoreDeeplink;
  return val;
}

const _$SuggestedTypeEnumMap = {
  SuggestedType.users: 'USERS',
  SuggestedType.circles: 'CIRCLES',
};
